from rest_framework import serializers
from .models import Client, ClientCommunication
from authentication.serializers import UserSerializer


class ClientSerializer(serializers.ModelSerializer):
    """Client serializer with all fields"""
    sales_rep = UserSerializer(read_only=True)
    sales_rep_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    mood_emoji = serializers.CharField(read_only=True)
    governorate_display = serializers.CharField(source='get_governorate_display', read_only=True)
    mood_display = serializers.CharField(source='get_mood_display', read_only=True)
    
    class Meta:
        model = Client
        fields = [
            'id', 'name', 'email', 'phone', 'company', 'website',
            'address', 'governorate', 'governorate_display',
            'mood', 'mood_display', 'mood_emoji',
            'sales_rep', 'sales_rep_id', 'notes',
            'total_projects', 'total_revenue',
            'last_contact_date', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'total_projects', 'total_revenue', 'created_at', 'updated_at']
    
    def validate_email(self, value):
        """Validate email uniqueness"""
        instance = getattr(self, 'instance', None)
        if Client.objects.filter(email=value).exclude(pk=instance.pk if instance else None).exists():
            raise serializers.ValidationError('البريد الإلكتروني مستخدم بالفعل')
        return value


class ClientListSerializer(serializers.ModelSerializer):
    """Simplified client serializer for lists"""
    sales_rep_name = serializers.CharField(source='sales_rep.full_name', read_only=True)
    mood_emoji = serializers.CharField(read_only=True)
    governorate_display = serializers.CharField(source='get_governorate_display', read_only=True)
    
    class Meta:
        model = Client
        fields = [
            'id', 'name', 'email', 'phone', 'company',
            'governorate', 'governorate_display',
            'mood', 'mood_emoji', 'sales_rep_name',
            'total_projects', 'total_revenue',
            'last_contact_date', 'created_at'
        ]


class ClientCreateSerializer(serializers.ModelSerializer):
    """Client creation serializer"""
    sales_rep_id = serializers.IntegerField(required=False, allow_null=True)
    
    class Meta:
        model = Client
        fields = [
            'name', 'email', 'phone', 'company', 'website',
            'address', 'governorate', 'mood', 'sales_rep_id', 'notes'
        ]
    
    def validate_email(self, value):
        """Validate email uniqueness"""
        if Client.objects.filter(email=value).exists():
            raise serializers.ValidationError('البريد الإلكتروني مستخدم بالفعل')
        return value


class ClientCommunicationSerializer(serializers.ModelSerializer):
    """Client communication serializer"""
    user = UserSerializer(read_only=True)
    user_id = serializers.IntegerField(write_only=True, required=False)
    type_display = serializers.CharField(source='get_type_display', read_only=True)
    
    class Meta:
        model = ClientCommunication
        fields = [
            'id', 'client', 'type', 'type_display', 'subject', 'content',
            'user', 'user_id', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def create(self, validated_data):
        """Create communication record with current user"""
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class ClientCommunicationCreateSerializer(serializers.ModelSerializer):
    """Client communication creation serializer"""
    
    class Meta:
        model = ClientCommunication
        fields = ['type', 'subject', 'content']
    
    def create(self, validated_data):
        """Create communication record with client and user from context"""
        validated_data['client'] = self.context['client']
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class ClientStatsSerializer(serializers.Serializer):
    """Client statistics serializer"""
    total_clients = serializers.IntegerField()
    new_this_month = serializers.IntegerField()
    happy_clients = serializers.IntegerField()
    concerned_clients = serializers.IntegerField()
    angry_clients = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=14, decimal_places=2)
    avg_projects_per_client = serializers.FloatField()
    top_governorates = serializers.ListField()
    mood_distribution = serializers.DictField()


class ClientDetailSerializer(serializers.ModelSerializer):
    """Detailed client serializer with related data"""
    sales_rep = UserSerializer(read_only=True)
    mood_emoji = serializers.CharField(read_only=True)
    governorate_display = serializers.CharField(source='get_governorate_display', read_only=True)
    mood_display = serializers.CharField(source='get_mood_display', read_only=True)
    recent_communications = ClientCommunicationSerializer(
        source='communications', many=True, read_only=True
    )
    projects_count = serializers.IntegerField(source='projects.count', read_only=True)
    
    class Meta:
        model = Client
        fields = [
            'id', 'name', 'email', 'phone', 'company', 'website',
            'address', 'governorate', 'governorate_display',
            'mood', 'mood_display', 'mood_emoji',
            'sales_rep', 'notes', 'total_projects', 'total_revenue',
            'projects_count', 'last_contact_date',
            'recent_communications', 'created_at', 'updated_at'
        ]
