'use client';

import { useAuthStore } from '@/lib/stores/auth-store';
import { UserRole, ROLE_PERMISSIONS } from '@mtbrmg/shared';

export function useNavigation() {
  const { user } = useAuthStore();

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    
    const userRole = user.role as keyof typeof ROLE_PERMISSIONS;
    const permissions = ROLE_PERMISSIONS[userRole];
    
    // Admin has all permissions
    if (permissions.includes('*')) return true;
    
    return permissions.includes(permission as any);
  };

  const hasRole = (role: UserRole): boolean => {
    if (!user) return false;
    return user.role === role;
  };

  const hasAnyRole = (roles: UserRole[]): boolean => {
    if (!user) return false;
    return roles.includes(user.role as UserRole);
  };

  const canAccessRoute = (route: string): boolean => {
    if (!user) return false;

    // Route-based permissions
    const routePermissions: Record<string, string[]> = {
      '/dashboard': ['*'],
      '/founder-dashboard': ['admin'],
      '/dashboard/clients': ['clients:read'],
      '/dashboard/projects': ['projects:read'],
      '/dashboard/tasks': ['tasks:read'],
      '/dashboard/team': ['admin', 'sales_manager'],
      '/dashboard/analytics': ['analytics:read'],
      '/dashboard/users': ['admin'],
      '/dashboard/settings': ['*'],
    };

    const requiredPermissions = routePermissions[route];
    if (!requiredPermissions) return true; // Allow access if no specific permissions defined

    // Check if user has admin role (access to everything)
    if (user.role === UserRole.ADMIN) return true;

    // Check specific permissions
    return requiredPermissions.some(permission => {
      if (permission === 'admin') return user.role === UserRole.ADMIN;
      if (permission === 'sales_manager') return user.role === UserRole.SALES_MANAGER;
      return hasPermission(permission);
    });
  };

  const getRoleDisplayName = (role?: string): string => {
    const roleMap: Record<string, string> = {
      admin: 'مدير النظام',
      sales_manager: 'مدير المبيعات',
      media_buyer: 'مشتري الإعلانات',
      developer: 'مطور',
      designer: 'مصمم',
      wordpress_developer: 'مطور ووردبريس',
    };
    return roleMap[role || ''] || role || '';
  };

  const getAvailableModules = () => {
    if (!user) return [];

    const allModules = [
      { key: 'dashboard', name: 'لوحة التحكم الرئيسية', permission: '*' },
      { key: 'founder-dashboard', name: 'لوحة تحكم المؤسس', permission: 'admin' },
      { key: 'clients', name: 'إدارة العملاء', permission: 'clients:read' },
      { key: 'projects', name: 'إدارة المشاريع', permission: 'projects:read' },
      { key: 'tasks', name: 'إدارة المهام', permission: 'tasks:read' },
      { key: 'team', name: 'إدارة الفريق', permission: 'admin' },
      { key: 'analytics', name: 'التقارير والإحصائيات', permission: 'analytics:read' },
      { key: 'users', name: 'إدارة المستخدمين', permission: 'admin' },
      { key: 'settings', name: 'الإعدادات', permission: '*' },
    ];

    return allModules.filter(module => {
      if (module.permission === '*') return true;
      if (module.permission === 'admin') return user.role === UserRole.ADMIN;
      return hasPermission(module.permission);
    });
  };

  return {
    user,
    hasPermission,
    hasRole,
    hasAnyRole,
    canAccessRoute,
    getRoleDisplayName,
    getAvailableModules,
  };
}
