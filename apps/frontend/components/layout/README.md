# Unified Layout System Documentation

## Overview

The Unified Layout System provides a consistent, role-based layout structure for the MTBRMG ERP application. It includes a sidebar, header, footer, and main content area that adapts to different user roles and supports RTL layout for Arabic language.

## Components

### 1. UnifiedLayout (Main Layout Wrapper)

The main layout component that combines all layout elements.

```tsx
import { UnifiedLayout } from '@/components/layout';

export default function MyPage() {
  return (
    <UnifiedLayout>
      <div>Your page content here</div>
    </UnifiedLayout>
  );
}
```

**Props:**
- `children`: ReactNode - The main content to display
- `className?`: string - Additional CSS classes
- `showSidebar?`: boolean - Whether to show the sidebar (default: true)
- `showHeader?`: boolean - Whether to show the header (default: true)
- `showFooter?`: boolean - Whether to show the footer (default: true)

### 2. UnifiedSidebar

Role-based navigation sidebar with company branding and quick stats.

**Features:**
- Role-based navigation filtering
- Company logo integration
- Collapsible menu items
- Quick stats section
- RTL support
- Responsive design

**Navigation Items:**
- لوحة التحكم الرئيسية (Main Dashboard) - All roles
- لوحة تحكم المؤسس (Founder Dashboard) - Admin only
- إدارة العملاء (Client Management) - Admin, Sales Manager, Media Buyer
- إدارة المشاريع (Project Management) - Admin, Sales Manager, Developer, Designer, WordPress Developer
- إدارة المهام (Task Management) - All roles
- إدارة الفريق (Team Management) - Admin, Sales Manager
- التقارير والإحصائيات (Reports & Analytics) - Admin, Sales Manager, Media Buyer
- إدارة المستخدمين (User Management) - Admin only
- الإعدادات (Settings) - All roles with sub-menus

### 3. UnifiedHeader

Consistent header with user profile, notifications, and global actions.

**Features:**
- User profile dropdown
- Notifications center
- Language switcher (Arabic/English)
- Theme switcher (Light/Dark)
- Search functionality
- Mobile menu integration
- RTL support

### 4. UnifiedFooter

Company branding and useful links footer.

**Features:**
- Company information
- Quick links
- Support resources
- Copyright information
- Version information
- RTL support

## Role-Based Access Control

The layout system integrates with the authentication system to provide role-based navigation:

### User Roles:
- **Admin**: Full access to all modules
- **Sales Manager**: Client management, projects, tasks, team, analytics
- **Media Buyer**: Clients, projects, tasks, analytics
- **Developer**: Projects, tasks
- **Designer**: Projects, tasks
- **WordPress Developer**: Projects, tasks

### Permission System:
The layout uses the `ROLE_PERMISSIONS` constant from the shared package to determine access:

```tsx
import { ROLE_PERMISSIONS } from '@mtbrmg/shared';
```

## Usage Examples

### Basic Usage
```tsx
import { UnifiedLayout } from '@/components/layout';

export default function DashboardPage() {
  return (
    <UnifiedLayout>
      <h1>Dashboard Content</h1>
    </UnifiedLayout>
  );
}
```

### Custom Layout Options
```tsx
import { UnifiedLayout } from '@/components/layout';

export default function FullScreenPage() {
  return (
    <UnifiedLayout showSidebar={false} showFooter={false}>
      <div className="h-screen">Full screen content</div>
    </UnifiedLayout>
  );
}
```

### Using Navigation Hook
```tsx
import { useNavigation } from '@/hooks/use-navigation';

export default function MyComponent() {
  const { hasPermission, canAccessRoute, getRoleDisplayName } = useNavigation();
  
  if (!hasPermission('clients:read')) {
    return <div>Access denied</div>;
  }
  
  return <div>Content for authorized users</div>;
}
```

## Styling and Theming

### CSS Classes
The layout system uses Tailwind CSS with custom Arabic font support:

```css
.font-arabic {
  font-family: var(--font-ibm-plex-arabic), "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}
```

### RTL Support
All components support RTL layout with `dir="rtl"` attribute and appropriate styling.

### Brand Colors
```css
--brand-primary: #6e1cc8
--brand-secondary: #6025a6
--brand-accent: #9865d3
--brand-light: #731dd0
```

## Mobile Responsiveness

- **Desktop**: Full sidebar visible
- **Tablet**: Collapsible sidebar
- **Mobile**: Sheet-based sidebar overlay

## Integration with Existing Pages

To migrate existing pages to use the unified layout:

1. Import the UnifiedLayout component
2. Wrap your page content with UnifiedLayout
3. Remove existing sidebar/header/footer code
4. Update navigation logic to use the new system

## File Structure

```
components/layout/
├── unified-layout.tsx      # Main layout wrapper
├── unified-sidebar.tsx     # Navigation sidebar
├── unified-header.tsx      # Header component
├── unified-footer.tsx      # Footer component
├── index.ts               # Export file
└── README.md              # This documentation
```

## Dependencies

- Next.js 14+
- React 18+
- Tailwind CSS
- ShadCN UI components
- Lucide React icons
- Zustand (for auth store)
- IBM Plex Sans Arabic font

## Future Enhancements

- Theme persistence
- Advanced notification system
- Customizable sidebar width
- More granular permissions
- Dashboard widgets system
- Advanced search functionality
