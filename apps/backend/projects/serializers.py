from rest_framework import serializers
from .models import Project
from clients.serializers import ClientListSerializer
from authentication.serializers import UserListSerializer


class ProjectSerializer(serializers.ModelSerializer):
    """Project serializer with all fields"""
    client = ClientListSerializer(read_only=True)
    client_id = serializers.IntegerField(write_only=True)
    assigned_team = UserListSerializer(many=True, read_only=True)
    assigned_team_ids = serializers.ListField(
        child=serializers.IntegerField(), write_only=True, required=False
    )
    project_manager = UserListSerializer(read_only=True)
    project_manager_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    type_display = serializers.CharField(source='get_type_display', read_only=True)
    
    class Meta:
        model = Project
        fields = [
            'id', 'name', 'description', 'type', 'type_display',
            'status', 'status_display', 'priority', 'priority_display',
            'client', 'client_id', 'assigned_team', 'assigned_team_ids',
            'project_manager', 'project_manager_id',
            'start_date', 'end_date', 'deadline',
            'budget', 'actual_cost', 'progress',
            'domains', 'repository_url', 'staging_url', 'production_url',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def create(self, validated_data):
        """Create project with team assignments"""
        assigned_team_ids = validated_data.pop('assigned_team_ids', [])
        project = Project.objects.create(**validated_data)
        
        if assigned_team_ids:
            project.assigned_team.set(assigned_team_ids)
        
        return project
    
    def update(self, instance, validated_data):
        """Update project with team assignments"""
        assigned_team_ids = validated_data.pop('assigned_team_ids', None)
        
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        if assigned_team_ids is not None:
            instance.assigned_team.set(assigned_team_ids)
        
        return instance


class ProjectListSerializer(serializers.ModelSerializer):
    """Simplified project serializer for lists"""
    client_name = serializers.CharField(source='client.name', read_only=True)
    project_manager_name = serializers.CharField(source='project_manager.full_name', read_only=True)
    team_count = serializers.IntegerField(source='assigned_team.count', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    type_display = serializers.CharField(source='get_type_display', read_only=True)
    
    class Meta:
        model = Project
        fields = [
            'id', 'name', 'type', 'type_display',
            'status', 'status_display', 'priority', 'priority_display',
            'client_name', 'project_manager_name', 'team_count',
            'start_date', 'end_date', 'deadline', 'progress',
            'budget', 'actual_cost', 'created_at'
        ]


class ProjectCreateSerializer(serializers.ModelSerializer):
    """Project creation serializer"""
    assigned_team_ids = serializers.ListField(
        child=serializers.IntegerField(), required=False, allow_empty=True
    )
    
    class Meta:
        model = Project
        fields = [
            'name', 'description', 'type', 'status', 'priority',
            'client', 'assigned_team_ids', 'project_manager_id',
            'start_date', 'end_date', 'deadline', 'budget',
            'domains', 'repository_url', 'staging_url', 'production_url'
        ]
    
    def create(self, validated_data):
        """Create project with team assignments"""
        assigned_team_ids = validated_data.pop('assigned_team_ids', [])
        project = Project.objects.create(**validated_data)
        
        if assigned_team_ids:
            project.assigned_team.set(assigned_team_ids)
        
        return project


class ProjectDetailSerializer(serializers.ModelSerializer):
    """Detailed project serializer with related data"""
    client = ClientListSerializer(read_only=True)
    assigned_team = UserListSerializer(many=True, read_only=True)
    project_manager = UserListSerializer(read_only=True)
    
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    type_display = serializers.CharField(source='get_type_display', read_only=True)
    
    tasks_count = serializers.IntegerField(source='tasks.count', read_only=True)
    completed_tasks = serializers.SerializerMethodField()
    pending_tasks = serializers.SerializerMethodField()
    
    class Meta:
        model = Project
        fields = [
            'id', 'name', 'description', 'type', 'type_display',
            'status', 'status_display', 'priority', 'priority_display',
            'client', 'assigned_team', 'project_manager',
            'start_date', 'end_date', 'deadline',
            'budget', 'actual_cost', 'progress',
            'domains', 'repository_url', 'staging_url', 'production_url',
            'tasks_count', 'completed_tasks', 'pending_tasks',
            'created_at', 'updated_at'
        ]
    
    def get_completed_tasks(self, obj):
        """Get count of completed tasks"""
        return obj.tasks.filter(status='completed').count()
    
    def get_pending_tasks(self, obj):
        """Get count of pending tasks"""
        return obj.tasks.exclude(status='completed').count()


class ProjectStatsSerializer(serializers.Serializer):
    """Project statistics serializer"""
    total_projects = serializers.IntegerField()
    active_projects = serializers.IntegerField()
    completed_projects = serializers.IntegerField()
    overdue_projects = serializers.IntegerField()
    total_budget = serializers.DecimalField(max_digits=14, decimal_places=2)
    total_actual_cost = serializers.DecimalField(max_digits=14, decimal_places=2)
    avg_progress = serializers.FloatField()
    status_distribution = serializers.DictField()
    type_distribution = serializers.DictField()
    priority_distribution = serializers.DictField()


class ProjectUpdateProgressSerializer(serializers.Serializer):
    """Project progress update serializer"""
    progress = serializers.IntegerField(min_value=0, max_value=100)
    notes = serializers.CharField(required=False, allow_blank=True)
